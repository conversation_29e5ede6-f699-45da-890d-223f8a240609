# WordPress plugin development ignores
# ------------------------------------

# Dependency directories
node_modules/
vendor/
# 保留插件的备用资源文件
!assets/vendor/

# AI tooling and workspace metadata
.cursor/
.augment/
.claude/

# IDE / Editor settings
.vscode/
.idea/
*.iml

# OS-generated files
.DS_Store
Thumbs.db
desktop.ini

# Logs and debug output
*.log
debug.log
logs/

# Environment / secrets
.env
.env.*

# Build & release artifacts
build/
*.zip
*.tar.gz
*.tgz
*.rar
*.7z

# Coverage / test output
coverage/

# Misc
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Temporary files
*.swp
*.bak
*.tmp

# Other plugin runtime caches/uploads (if generated inside plugin folder)
uploads/
.cache/ 